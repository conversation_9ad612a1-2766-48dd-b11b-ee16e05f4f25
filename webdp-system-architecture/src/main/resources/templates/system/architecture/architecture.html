<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('体系结构列表')"/>
    <th:block th:include="include :: layout-latest-css"/>
    <th:block th:include="include :: select2-css"/>
    <th:block th:include="include :: ztree-css"/>
</head>
<body class="gray-bg">
<div class="ui-layout-west">
    <div class="box box-main">
        <div class="box-header">
            <div class="box-title">
                <i class="fa icon-grid"></i> 待编辑体系结构
            </div>
            <div class="box-tools pull-right">
                <button type="button" class="btn btn-box-tool" id="btnExpand" title="展开" style="display:none;"><i class="fa fa-chevron-up"></i></button>
                <button type="button" class="btn btn-box-tool" id="btnCollapse" title="折叠"><i class="fa fa-chevron-down"></i></button>
                <button type="button" class="btn btn-box-tool" id="btnRefresh" title="刷新体系结构"><i class="fa fa-refresh"></i></button>
                <button type="button" class="btn btn-box-tool" onclick="add()" id="addArchitectureButton" disabled title="添加" shiro:hasPermission="system:architecture:add"><i class="fa fa-plus"></i></button>
                <button type="button" class="btn btn-box-tool" onclick="edit()" id="editArchitectureButton" disabled title="修改" shiro:hasPermission="system:architecture:edit"><i class="fa fa-edit"></i></button>
                <button type="button" class="btn btn-box-tool" onclick="remove()" id="removeArchitectureButton" disabled title="删除" shiro:hasPermission="system:architecture:remove"><i class="fa fa-remove"></i></button>
            </div>
        </div>
        <div class="ui-layout-content">
            <div id="tree" class="ztree"></div>
        </div>
    </div>
</div>

<div class="ui-layout-center">
    <div class="row" style="height: 100%;background-color: #ffffff">
        <div class="ibox float-e-margins">
            <div class="ibox-title">
                <h5 id="structureTitle">机构库</h5>
            </div>
            <div class="ibox-content">
                <div class="row">
                    <form id="form-architecture" class=" pull-left">
                        <input name="systemId" id="systemId" th:value="${systemId}" type="hidden">
                        <input name="parentId" id="parentId" value="0" type="hidden">
                        <input name="selectOrgCode" id="selectOrgCode" type="hidden">
                        <input name="parentOrgCode" id="parentOrgCode" type="hidden">
                        <div class="select-list">
                            <ul>
                                <li>
                                    <label style="width: 100px;">国家/地区：</label>
                                    <select class="form-control" name="country" id="country"
                                            th:with="type=${@dict.getType('sys_country')}">
                                        <option value="">所有</option>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                th:value="${dict.dictValue}"></option>
                                    </select>
                                </li>
                                <li>
                                    <label>机构名称：</label>
                                    <input type="text" name="orgNameCn" id="orgNameCn"/>
                                </li>
                                <li>
                                    <label>机构类型：</label>
                                    <select class="form-control" name="orgType" id="orgType"
                                            th:with="type=${@dict.getType('sys_org_type')}">
                                        <option value="">所有</option>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                th:value="${dict.dictValue}"></option>
                                    </select>
                                </li>
                                <li>
                                    <a class="btn btn-primary btn-rounded btn-sm"
                                       onclick="$.table.search('form-architecture', 'bootstrap-table-architecture')"><i
                                            class="fa fa-search"></i>&nbsp;搜索</a>
                                    <a class="btn btn-warning btn-rounded btn-sm" onclick="reset()"><i
                                            class="fa fa-refresh"></i>&nbsp;重置</a>
                                </li>
                            </ul>
                        </div>
                    </form>
                    <div class="btn-group-sm" id="toolbar-architecture" role="group">
                        <a class="btn btn-warning disabled" onclick="addOrganizations()" id="addOrganizations"
                           shiro:hasPermission="system:architecture:add"><i class="fa fa-plus"></i> 添加到左侧节点
                        </a>
                    </div>
                    <div class="col-sm-12 select-table table-striped">
                        <table id="bootstrap-table-architecture"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: ztree-js"/>
<th:block th:include="include :: select2-js"/>
<th:block th:include="include :: layout-latest-js"/>
<script th:inline="javascript">
    let removeFlag = [[${@permission.hasPermi('system:architecture:remove')}]];
    let editFlag = [[${@permission.hasPermi('system:architecture:edit')}]];
    let countryDatas = [[${@dict.getType('sys_country')}]]
    let prefix = ctx + "system/architecture";

    $(function () {
        let panehHidden = false;
        if ($(this).width() < 769) {
            panehHidden = true;
        }
        $('body').layout({initClosed: panehHidden, west__size: 450});
        // 回到顶部绑定
        if ($.fn.toTop !== undefined) {
            var opt = {
                win: $('.ui-layout-center'),
                doc: $('.ui-layout-center')
            };
            $("#addOrganization").attr('class', 'btn btn-warning disabled');
            queryTree()
            $('#scroll-up').toTop(opt);
        }
    })

    $(function () {
        let options = {
            id: "bootstrap-table-architecture",          // 指定表格ID
            toolbar: "toolbar-architecture",   // 指定工具栏ID
            formId: "form-architecture",
            url: prefix + "/orgList",
            modalName: "体系结构",
            uniqueId: 'orgCode',
            rememberSelected: true,
            onCheck: onCheck,
            onUncheck: onUncheck,
            onCheckAll: onCheckAll,
            onUncheckAll: onUncheckAll,
            columns: [{
                field: 'state',
                checkbox: true,
                formatter: function (value, row, index) {
                    let selectOrgCode = $('#selectOrgCode').val();
                    let orgCode = row.orgCode;
                    if ((',' + selectOrgCode + ',').includes(',' + orgCode + ',')) {
                        return {checked: true}
                    } else {
                        return {checked: false}
                    }
                }
            },
                {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    formatter: function (value, row, index) {
                        var columnIndex = $.common.sprintf("<input type='hidden' name='index' value='%s'>", $.table.serialNumber(index));
                        return columnIndex + $.table.serialNumber(index);
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        let actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="addOrganization(\'' + row.orgCode + '\')"><i class="fa fa-plus"></i>添加到左侧节点</a> ');
                        return actions.join('');
                    }
                },
                {
                    align: 'center',
                    field: 'architectureCount',
                    title: '体系架构数量',
                    formatter: function (value, row, index) {
                        if (value > 0) {
                            return '<a onclick="architectureCountTree(\'' + row.orgCode + '\')">' + value + '</a>';
                        } else {
                            return value;
                        }
                    }
                },
                {
                    field: 'orgNameCn',
                    title: '中文名称名称',
                    formatter: function (value, row, index) {
                        return $.table.tooltip(value, 8);
                    }
                },
                {
                    field: 'orgNameEn',
                    title: '外文名称名称',
                    formatter: function (value, row, index) {
                        return $.table.tooltip(value, 20);
                    }
                },
                {
                    field: 'country',
                    title: '国家/地区',
                    width: 100,
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(countryDatas, value);
                    }
                },
                {
                    field: 'address',
                    title: '地址',
                    formatter: function (value, row, index) {
                        return $.table.tooltip(value, 16);
                    }
                },
                {
                    field: 'orgType',
                    title: '机构类型'
                }]
        };
        $.table.init(options);
    });

    //选中行
    function onCheck(row, $element) {
        let orgCode = row.orgCode;
        $("#addOrganizations").attr('class', 'btn btn-warning');
        let selectOrgCode = $('#selectOrgCode').val().replaceAll(',,', ',');
        if (!(',' + selectOrgCode + ',').includes(',' + orgCode + ',')) {
            if (selectOrgCode !== null && selectOrgCode !== '' && selectOrgCode !== undefined) {
                selectOrgCode += ',' + orgCode
            } else {
                selectOrgCode = orgCode
            }
            $('#selectOrgCode').val(selectOrgCode)
        }
    }

    //取消选中行
    function onUncheck(row, $element) {
        let orgCode = row.orgCode;
        let selectOrgCode = $('#selectOrgCode').val();
        if ((',' + selectOrgCode + ',').includes(',' + orgCode + ',')) {
            selectOrgCode = (',' + selectOrgCode + ',').replace(',' + orgCode + ',', ',').replaceAll(',,', ',')
            $('#selectOrgCode').val(selectOrgCode)
        }
        if (selectOrgCode.replaceAll(',', '') == '') {
            $("#addOrganizations").attr('class', 'btn btn-warning disabled');
            $('#selectOrgCode').val('')
        }
    }

    //全选行
    function onCheckAll(rowsAfter, rowsBefore) {
        $("#addOrganizations").attr('class', 'btn btn-warning');
        for (let i = 0; i < rowsAfter.length; i++) {
            onCheck(rowsAfter[i])
        }
    }

    //取消全选行
    function onUncheckAll(rowsAfter, rowsBefore) {
        for (let i = 0; i < rowsBefore.length; i++) {
            onUncheck(rowsBefore[i])
        }
        let selectOrgCode = $('#selectOrgCode').val();
        if (selectOrgCode.replaceAll(',', '') == '') {
            $("#addOrganizations").attr('class', 'btn btn-warning disabled');
            $('#selectOrgCode').val('')
        }
    }

    //体系架构数量
    function architectureCountTree(orgCode) {
        let options = {
            title: '选择体系结构',
            width: "380",
            url: prefix + "/architectureCountTree/" + systemId + '/' + $('#parentId').val() + '/' + orgCode,
            callBack: doSubmit
        };
        $.modal.openOptions(options);
    }

    function doSubmit(index, layero) {
        let body = layer.getChildFrame('body', index);
        let architectureId = body.find('#architectureId').val();
        if (architectureId !== null && architectureId !== '' && architectureId !== undefined) {
            $.ajax({
                type: "GET",
                url: prefix + "/addArchitecture/" + systemId + '/' + $('#parentId').val() + '/' + architectureId,
                async: false,
                success: function (res) {
                    $.modal.msgSuccess("操作成功！");
                    queryTree();
                    layer.close(index);
                }
            });
        } else {
            layer.close(index);
        }
    }

    //新增体系结构
    function add() {
        $.modal.open('添加体系结构', prefix + "/add/" + systemId + '/' + $('#parentId').val(), null, 500);
    }

    //修改体系结构
    function edit() {
        let parentId = $('#parentId').val();
        if (parentId != null && parentId != '' && parentId != undefined) {
            $.modal.open('修改体系结构', prefix + "/edit/" + parentId, null, 500);
        } else {
            $.modal.msgWarning('请先选择主题！')
        }
    }

    //关联机构
    function addOrganization(orgCode) {
        let parentOrgCode = $('#parentOrgCode').val()
        if (orgCode === parentOrgCode) {
            $.modal.msgWarning('不能将自己添加到下级！')
        } else {
            let parentId = $('#parentId').val()
            $.ajax({
                type: "GET",
                url: prefix + "/isExist/" + systemId + '/' + parentId + '/' + orgCode,
                async: false,
                success: function (isExist) {
                    if (!isExist) {
                        $.ajax({
                            type: "GET",
                            url: prefix + "/addOrgArchitecture/" + systemId + '/' + parentId + '/' + orgCode,
                            async: false,
                            success: function (res) {
                                $.modal.msgSuccess("操作成功！");
                                queryTree();
                                tableSearch();
                            }
                        });
                    } else {
                        $.modal.msgWarning('机构已存在！')
                    }
                }
            });
        }
    }

    //批量关联机构
    function addOrganizations() {
        let selectOrgCode = $('#selectOrgCode').val();
        let parentOrgCode = $('#parentOrgCode').val()
        selectOrgCode = (',' + selectOrgCode + ',').replace(',' + parentOrgCode + ',', ',').replaceAll(',,', ',')
        let parentId = $('#parentId').val()
        $.ajax({
            type: "POST",
            url: prefix + "/addOrgArchitectures",
            data: {
                'systemId': systemId,
                'parentId': parentId,
                'orgCodes': selectOrgCode,
            },
            async: false,
            success: function (res) {
                $.modal.msgSuccess("操作成功！");
                $('#selectOrgCode').val('')
                queryTree();
                tableSearch();
                $("#addOrganizations").attr('class', 'btn btn-warning disabled');
            }
        });
    }

    //删除体系结构
    function remove() {
        let parentId = $('#parentId').val();
        if (parentId != null && parentId != '' && parentId != undefined) {
            $.modal.confirm("确定删除选择体系结构极其下级吗？", function () {
                $.ajax({
                    type: "GET",
                    url: prefix + "/remove/" + parentId,
                    async: false,
                    success: function (result) {
                        if (result.code == web_status.SUCCESS) {
                            $.modal.msgSuccess("操作成功！");
                            $('#parentId').val('0')
                            queryTree();
                        } else {
                            $.modal.msgWarning(result.msg)
                        }
                    }
                });
            });
        } else {
            $.modal.msgWarning('请先选择体系结构！')
        }
    }

    function tableSearch() {
        $.table.search('form-architecture', 'bootstrap-table-architecture')
    }

    function reset() {
        $('#parentId').val('0')
        $('#orgNameCn').val('')
        $('#country').val('').trigger('change');
        $('#orgType').val('').trigger('change');
        tableSearch();
        queryTree();
    }

    let systemName = encodeURI(encodeURI([[${systemName}]]))
    let systemId = [[${systemId}]]

    //加载体系结构分类树
    function queryTree() {
        var url = prefix + "/tree/" + systemId + '/' + systemName;
        var options = {
            url: url,
            view: {
                showIcon: false,
                addDiyDom: addDiyDom
            },
            expandLevel: 2,
            onClick: onClick
        };
        $.tree.init(options);

        /**
         * 节点点击事件
         */
        function onClick(event, treeId, treeNode) {
            $('#parentOrgCode').val(treeNode.relatedCode)
            let id = treeNode.id;
            $('#parentId').val(id)
            if (id == 0) {
                document.getElementById('addArchitectureButton').disabled = true
                document.getElementById('editArchitectureButton').disabled = true
                document.getElementById('removeArchitectureButton').disabled = true
            } else {
                document.getElementById('addArchitectureButton').disabled = false
                document.getElementById('editArchitectureButton').disabled = false
                document.getElementById('removeArchitectureButton').disabled = false
            }
            tableSearch();
        }

        //在节点名称前拼接色块
        function addDiyDom(treeId, treeNode) {
            var aObj = $("#" + treeNode.tId + '_a');
            let colorLump = '<span style="padding: 0px 7.5px; border-radius: 50%; margin-left: 2px; background-color: ' + treeNode.color + ';"></span>'
            aObj.before(colorLump);
            let btn = $("#diyBtn_" + treeNode.id);
            if (btn) btn.bind("click", function () {
                alert("diy Button for " + treeNode.name);
            });
        }
    }

    $('#btnExpand').click(function () {
        $._tree.expandAll(true);
        $(this).hide();
        $('#btnCollapse').show();
    });

    $('#btnCollapse').click(function () {
        $._tree.expandAll(false);
        $(this).hide();
        $('#btnExpand').show();
    });

    $('#btnRefresh').click(function () {
        queryTree();
    });

    //展开所有节点
    $('#btnExpand').click(function () {
        var treeObj = $.fn.zTree.getZTreeObj("tree");
        treeObj.expandAll(true);
        $(this).hide();
        $('#btnCollapse').show();
    });

    //收缩所有节点
    $('#btnCollapse').click(function () {
        var treeObj = $.fn.zTree.getZTreeObj("tree");
        treeObj.expandAll(false);
        $(this).hide();
        $('#btnExpand').show();
    });

</script>
</body>
</html>