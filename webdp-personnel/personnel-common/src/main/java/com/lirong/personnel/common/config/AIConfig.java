package com.lirong.personnel.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * AI服务配置类
 */
@Component
@ConfigurationProperties(prefix = "ai.siliconflow")
public class AIConfig {
    
    /**
     * 硅基流动API地址
     */
    private String apiUrl = "https://api.siliconflow.cn/v1/chat/completions";
    
    /**
     * API密钥
     */
    private String apiKey = "sk-your-api-key-here";
    
    /**
     * 模型名称
     */
    private String model = "deepseek-ai/DeepSeek-V3";
    
    /**
     * 最大token数
     */
    private int maxTokens = 4000;
    
    /**
     * 温度参数
     */
    private double temperature = 0.1;
    
    /**
     * 连接超时时间（毫秒）
     */
    private int connectTimeout = 30000;
    
    /**
     * 读取超时时间（毫秒）
     */
    private int readTimeout = 120000;

    // Getter和Setter方法
    public String getApiUrl() {
        return apiUrl;
    }

    public void setApiUrl(String apiUrl) {
        this.apiUrl = apiUrl;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public int getMaxTokens() {
        return maxTokens;
    }

    public void setMaxTokens(int maxTokens) {
        this.maxTokens = maxTokens;
    }

    public double getTemperature() {
        return temperature;
    }

    public void setTemperature(double temperature) {
        this.temperature = temperature;
    }

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }
}
